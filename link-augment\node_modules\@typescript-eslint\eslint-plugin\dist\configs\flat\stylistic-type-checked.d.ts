import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * Contains all of `stylistic`, along with additional stylistic rules that require type information.
 * @see {@link https://typescript-eslint.io/users/configs#stylistic-type-checked}
 */
declare const _default: (plugin: FlatConfig.Plugin, parser: FlatConfig.Parser) => FlatConfig.ConfigArray;
export default _default;
//# sourceMappingURL=stylistic-type-checked.d.ts.map