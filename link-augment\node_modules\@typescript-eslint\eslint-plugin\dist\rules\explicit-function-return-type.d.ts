export type Options = [
    {
        allowConciseArrowFunctionExpressionsStartingWithVoid?: boolean;
        allowDirectConstAssertionInArrowFunctions?: boolean;
        allowedNames?: string[];
        allowExpressions?: boolean;
        allowFunctionsWithoutTypeParameters?: boolean;
        allowHigherOrderFunctions?: boolean;
        allowIIFEs?: boolean;
        allowTypedFunctionExpressions?: boolean;
    }
];
export type MessageIds = 'missingReturnType';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"missingReturnType", Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=explicit-function-return-type.d.ts.map